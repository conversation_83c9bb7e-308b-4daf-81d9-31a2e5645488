// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AED": MessageLookupByLibrary.simpleMessage("AED"),
    "Accepted": MessageLookupByLibrary.simpleMessage("Accepted"),
    "Activities": MessageLookupByLibrary.simpleMessage("Activities"),
    "ActivityName": MessageLookupByLibrary.simpleMessage("Activity Name"),
    "Actvities": MessageLookupByLibrary.simpleMessage("Actvities"),
    "AddAgent": MessageLookupByLibrary.simpleMessage("Add Agent"),
    "AddApplicationfeature": MessageLookupByLibrary.simpleMessage(
      "Add Application feature",
    ),
    "AddCaterogryfeature": MessageLookupByLibrary.simpleMessage(
      "Add Caterogry feature",
    ),
    "AddNewActivity": MessageLookupByLibrary.simpleMessage("Add New Activity"),
    "AddNewArea": MessageLookupByLibrary.simpleMessage("Add New Area"),
    "AddNewBrand": MessageLookupByLibrary.simpleMessage("Add New Brand"),
    "AddNewChalet": MessageLookupByLibrary.simpleMessage("Add New Chalet"),
    "AddNewDestination": MessageLookupByLibrary.simpleMessage(
      "Add New Destination",
    ),
    "AddNewFeature": MessageLookupByLibrary.simpleMessage("Add New Feature"),
    "AddNewPromoCode": MessageLookupByLibrary.simpleMessage(
      "Add New Promo Code",
    ),
    "AddNewRentalCars": MessageLookupByLibrary.simpleMessage(
      "+ Add New Rental Cars",
    ),
    "AddNewYear": MessageLookupByLibrary.simpleMessage("Add New Year"),
    "AddNewagent": MessageLookupByLibrary.simpleMessage("Add New agent"),
    "AddPropertyStatus": MessageLookupByLibrary.simpleMessage(
      "Add Property Status",
    ),
    "AddnewArea": MessageLookupByLibrary.simpleMessage("Add new Area"),
    "AddnewHolidayHome": MessageLookupByLibrary.simpleMessage(
      "Add new Holiday Home",
    ),
    "AddnewPromoCode": MessageLookupByLibrary.simpleMessage(
      "Add new Promo Code",
    ),
    "AddnewProperty": MessageLookupByLibrary.simpleMessage("Add new Property"),
    "Addnewhotel": MessageLookupByLibrary.simpleMessage("Add new hotel"),
    "Addnewrentalcar": MessageLookupByLibrary.simpleMessage(
      "Add new rental car",
    ),
    "Addnewrentalcars": MessageLookupByLibrary.simpleMessage(
      "Add new rental cars",
    ),
    "Addnewresturant": MessageLookupByLibrary.simpleMessage(
      "Add new resturant",
    ),
    "Addnewshop": MessageLookupByLibrary.simpleMessage("Add new shop"),
    "Addnewtype": MessageLookupByLibrary.simpleMessage("Add new type"),
    "AdminAccountInformation": MessageLookupByLibrary.simpleMessage(
      "Admin Account Information",
    ),
    "Agent": MessageLookupByLibrary.simpleMessage("Agent"),
    "AgentCompanyName": MessageLookupByLibrary.simpleMessage(
      "Agent Company Name",
    ),
    "AgentDetails": MessageLookupByLibrary.simpleMessage("Agent Details"),
    "AgentNote": MessageLookupByLibrary.simpleMessage("Agent Note"),
    "Agentcompanyname": MessageLookupByLibrary.simpleMessage(
      "Agent company name",
    ),
    "Agentname": MessageLookupByLibrary.simpleMessage("Agent name"),
    "Agentnote": MessageLookupByLibrary.simpleMessage("Agent note"),
    "Agents": MessageLookupByLibrary.simpleMessage("Agents"),
    "Agenty": MessageLookupByLibrary.simpleMessage("Agenty"),
    "AgreedPrice": MessageLookupByLibrary.simpleMessage("Agreed Price"),
    "AgreementTotalPrice": MessageLookupByLibrary.simpleMessage(
      "Agreement Total Price",
    ),
    "AlMalekFahedStreet": MessageLookupByLibrary.simpleMessage(
      "Al Malek Fahed Street",
    ),
    "AllAgents": MessageLookupByLibrary.simpleMessage("All Agents"),
    "AllAreas": MessageLookupByLibrary.simpleMessage("All Areas"),
    "AllCarBrands": MessageLookupByLibrary.simpleMessage("All Car Brands"),
    "AllCarYears": MessageLookupByLibrary.simpleMessage("All Car Years"),
    "AllCategories": MessageLookupByLibrary.simpleMessage("All Categories"),
    "AllDestinations": MessageLookupByLibrary.simpleMessage("All Destinations"),
    "AllFeaturedVideos": MessageLookupByLibrary.simpleMessage(
      "All Featured Videos",
    ),
    "AllFeatures": MessageLookupByLibrary.simpleMessage("All Features"),
    "AllHotels": MessageLookupByLibrary.simpleMessage("All Hotels"),
    "AllPromoCodes": MessageLookupByLibrary.simpleMessage("All Promo Codes"),
    "AllPromocodes": MessageLookupByLibrary.simpleMessage("All Promo codes"),
    "AllProperties": MessageLookupByLibrary.simpleMessage("All Properties"),
    "AllRentalCars": MessageLookupByLibrary.simpleMessage("AllRentalCars"),
    "AllRentalcars": MessageLookupByLibrary.simpleMessage("All Rental cars"),
    "AllRequests": MessageLookupByLibrary.simpleMessage("All Requests"),
    "AllUsers": MessageLookupByLibrary.simpleMessage("All Users"),
    "Allactivities": MessageLookupByLibrary.simpleMessage("All activities"),
    "Allchalets": MessageLookupByLibrary.simpleMessage("All chalets"),
    "Allholidayhomes": MessageLookupByLibrary.simpleMessage(
      "All holiday homes",
    ),
    "Allresturants": MessageLookupByLibrary.simpleMessage("All resturants"),
    "Allshops": MessageLookupByLibrary.simpleMessage("All Coffee Shops"),
    "Alltypes": MessageLookupByLibrary.simpleMessage("All types"),
    "Almalekfahedstreet": MessageLookupByLibrary.simpleMessage(
      "Al malek fahed street",
    ),
    "ApplyFilter": MessageLookupByLibrary.simpleMessage("Apply Filter"),
    "Arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "AreYouSureYouWantToDeleteThisProject":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this project?",
        ),
    "Areaname": MessageLookupByLibrary.simpleMessage("Area name"),
    "Areas": MessageLookupByLibrary.simpleMessage("Areas"),
    "AreyousureyouwanttodeletethisHolidayhome":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this Holiday home",
        ),
    "AreyousureyouwanttodeletethisShopifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this Shop,if yes you wont be able to see again",
        ),
    "Areyousureyouwanttodeletethisactivitcaroseeagain":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this Car Rental,if yes you wont be able to see again",
        ),
    "Areyousureyouwanttodeletethisactivityifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this activity,if yes you wont be able to see again",
        ),
    "Areyousureyouwanttodeletethisbrand": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this brand",
    ),
    "Areyousureyouwanttodeletethischaletifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this chalet,if yes you wont be able to see again",
        ),
    "Areyousureyouwanttodeletethisfeature":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this feature",
        ),
    "Areyousureyouwanttodeletethishotelifyesyouwontbeabletoseeagain":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this hotel,if yes you wont be able to see again",
        ),
    "Areyousureyouwanttodeletethispromocode":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this promo code",
        ),
    "Areyousureyouwanttodeletethisreelvideo":
        MessageLookupByLibrary.simpleMessage(
          "Are you sure you want to delete this reel video",
        ),
    "Areyousureyouwanttodeletethisrentalcar": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this rental car,if yes you wont be able to see again",
    ),
    "Areyousureyouwanttodeletethisresturant": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this resturant,if yes you wont be able to see again",
    ),
    "Areyousureyouwanttodeletethistype": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this type",
    ),
    "Areyousureyouwanttodeletethisyear": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this year,if yes you wont be able to see again",
    ),
    "Areyyousureyouwanttodeletethisagent": MessageLookupByLibrary.simpleMessage(
      "Are yyou sure you want to delete this agent",
    ),
    "AskingPrice": MessageLookupByLibrary.simpleMessage("Asking Price"),
    "Assignasfeaturedvideoincatpage": MessageLookupByLibrary.simpleMessage(
      "Assign as featured video in category page",
    ),
    "Assignasfeaturedvideoinhomepage": MessageLookupByLibrary.simpleMessage(
      "Assign as featured video in home page",
    ),
    "Averagep": MessageLookupByLibrary.simpleMessage("Average price"),
    "Averagepriceper": MessageLookupByLibrary.simpleMessage(
      "Average price(per person)",
    ),
    "BMW": MessageLookupByLibrary.simpleMessage("BMW"),
    "Balcony": MessageLookupByLibrary.simpleMessage("Balcony"),
    "BasicInformation": MessageLookupByLibrary.simpleMessage(
      "Basic Information",
    ),
    "Basicinformation": MessageLookupByLibrary.simpleMessage(
      "Basic information",
    ),
    "Bedrooms": MessageLookupByLibrary.simpleMessage("Bedrooms"),
    "Bigrooms": MessageLookupByLibrary.simpleMessage("Big rooms"),
    "Brand": MessageLookupByLibrary.simpleMessage("Brand"),
    "BrandName": MessageLookupByLibrary.simpleMessage("Brand Name"),
    "BurjKhalifaview": MessageLookupByLibrary.simpleMessage(
      "Burj Khalifa view",
    ),
    "CallAgent": MessageLookupByLibrary.simpleMessage("Call Agent"),
    "Callclient": MessageLookupByLibrary.simpleMessage("Call client"),
    "Cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "Canceled": MessageLookupByLibrary.simpleMessage("Canceled"),
    "CarBrands": MessageLookupByLibrary.simpleMessage("Car Brands"),
    "CarName": MessageLookupByLibrary.simpleMessage("Car Name"),
    "CarProductionYear": MessageLookupByLibrary.simpleMessage(
      "Car Production Year",
    ),
    "CarRental": MessageLookupByLibrary.simpleMessage("Car Rental"),
    "CarYears": MessageLookupByLibrary.simpleMessage("Car Years"),
    "Carbrads": MessageLookupByLibrary.simpleMessage("Car brads"),
    "Carname": MessageLookupByLibrary.simpleMessage("Car name"),
    "Carrental": MessageLookupByLibrary.simpleMessage("Car rental"),
    "CarrentalAgets": MessageLookupByLibrary.simpleMessage("Car rental Agets"),
    "Categories": MessageLookupByLibrary.simpleMessage("Categories"),
    "Category": MessageLookupByLibrary.simpleMessage("Category"),
    "Chalet": MessageLookupByLibrary.simpleMessage("Chalet"),
    "Chalets": MessageLookupByLibrary.simpleMessage("Chalets"),
    "ChangePassword": MessageLookupByLibrary.simpleMessage("Change Password"),
    "Chevrolet": MessageLookupByLibrary.simpleMessage("Chevrolet"),
    "ChooseCategories": MessageLookupByLibrary.simpleMessage(
      "Choose Categories",
    ),
    "ChooseCategory": MessageLookupByLibrary.simpleMessage("Choose Category"),
    "Code": MessageLookupByLibrary.simpleMessage("Code"),
    "Configuration": MessageLookupByLibrary.simpleMessage("Configuration"),
    "ConfirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm New Password",
    ),
    "Contactfinishdate": MessageLookupByLibrary.simpleMessage(
      "Contact finish date",
    ),
    "Continue": MessageLookupByLibrary.simpleMessage("Continue"),
    "ContractFinishDate": MessageLookupByLibrary.simpleMessage(
      "Contract Finish Date",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("Country"),
    "DayPrice": MessageLookupByLibrary.simpleMessage("Day Price"),
    "DeleteActivity": MessageLookupByLibrary.simpleMessage("Delete Activity"),
    "DeleteAgent": MessageLookupByLibrary.simpleMessage("Delete Agent"),
    "DeleteArea": MessageLookupByLibrary.simpleMessage("Delete Area"),
    "DeleteBrand": MessageLookupByLibrary.simpleMessage("Delete Brand"),
    "DeleteChalet": MessageLookupByLibrary.simpleMessage("Delete Chalet"),
    "DeleteComment": MessageLookupByLibrary.simpleMessage("Delete Comment"),
    "DeleteHolidayhome": MessageLookupByLibrary.simpleMessage(
      "Delete Holiday home",
    ),
    "DeleteProject": MessageLookupByLibrary.simpleMessage("Delete Project"),
    "DeletePromoCode": MessageLookupByLibrary.simpleMessage(
      "Delete Promo Code",
    ),
    "DeleteProperty": MessageLookupByLibrary.simpleMessage("Delete Property"),
    "DeleteReelVideo": MessageLookupByLibrary.simpleMessage(
      "Delete Reel Video",
    ),
    "DeleteRentalcar": MessageLookupByLibrary.simpleMessage(
      "Delete Rental car",
    ),
    "DeleteResturant": MessageLookupByLibrary.simpleMessage("Delete Resturant"),
    "DeleteShop": MessageLookupByLibrary.simpleMessage("Delete Shop"),
    "DeleteYear": MessageLookupByLibrary.simpleMessage("Delete Year"),
    "Deletefeature": MessageLookupByLibrary.simpleMessage("Delete feature"),
    "Deletehotel": MessageLookupByLibrary.simpleMessage("Delete hotel"),
    "Deletetype": MessageLookupByLibrary.simpleMessage("Delete type"),
    "Denied": MessageLookupByLibrary.simpleMessage("Denied"),
    "Description": MessageLookupByLibrary.simpleMessage("Description"),
    "Destination": MessageLookupByLibrary.simpleMessage("Destination"),
    "Destinations": MessageLookupByLibrary.simpleMessage("Destinations"),
    "Discount": MessageLookupByLibrary.simpleMessage("Discount"),
    "DiscountPercentage": MessageLookupByLibrary.simpleMessage(
      "Discount Percentage",
    ),
    "Discussion": MessageLookupByLibrary.simpleMessage("Discussion"),
    "DriverPricePerDay": MessageLookupByLibrary.simpleMessage(
      "Driver Price Per Day",
    ),
    "DriverPriceperday": MessageLookupByLibrary.simpleMessage(
      "Driver Price per day",
    ),
    "Dubaicitycenter": MessageLookupByLibrary.simpleMessage(
      "Dubai city center",
    ),
    "Edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "EditProject": MessageLookupByLibrary.simpleMessage("Edit Project"),
    "EditReelvideos": MessageLookupByLibrary.simpleMessage("Edit Reel videos"),
    "EditYear": MessageLookupByLibrary.simpleMessage("Edit Year"),
    "Editcomment": MessageLookupByLibrary.simpleMessage("Edi tcomment"),
    "Editimages": MessageLookupByLibrary.simpleMessage("Edit images"),
    "Email": MessageLookupByLibrary.simpleMessage("Email"),
    "EmailAddress": MessageLookupByLibrary.simpleMessage("Email Address"),
    "EmailAdress": MessageLookupByLibrary.simpleMessage("Email Adress"),
    "English": MessageLookupByLibrary.simpleMessage("English"),
    "EnterNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter New Password",
    ),
    "EnteraNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter a New Password,that is easy to remember",
    ),
    "EnterdigitsOTP": MessageLookupByLibrary.simpleMessage(
      "Enter 4-digits OTP",
    ),
    "ErrorLoadingProjects": MessageLookupByLibrary.simpleMessage(
      "Error loading projects",
    ),
    "Exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "ExtraInformation": MessageLookupByLibrary.simpleMessage(
      "Extra Information",
    ),
    "Farm": MessageLookupByLibrary.simpleMessage("Farm"),
    "FeaturedVideos": MessageLookupByLibrary.simpleMessage("Featured Videos"),
    "Features": MessageLookupByLibrary.simpleMessage("Features"),
    "Fee": MessageLookupByLibrary.simpleMessage("Fee"),
    "FeturedVideos": MessageLookupByLibrary.simpleMessage("Fetured Videos"),
    "Filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "Filteredas": MessageLookupByLibrary.simpleMessage("Filteredas"),
    "ForgotPassword": MessageLookupByLibrary.simpleMessage("Forgot Password"),
    "From": MessageLookupByLibrary.simpleMessage("From"),
    "Fullname": MessageLookupByLibrary.simpleMessage("Full name English"),
    "FullnameAr": MessageLookupByLibrary.simpleMessage("Full Name Arabic"),
    "GoogleReviewLink": MessageLookupByLibrary.simpleMessage(
      "Google Review Link",
    ),
    "GoogleReviewName": MessageLookupByLibrary.simpleMessage(
      "Google Review Name",
    ),
    "HideComment": MessageLookupByLibrary.simpleMessage("Hide Comment"),
    "HolidayHome": MessageLookupByLibrary.simpleMessage("Holiday Home"),
    "HolidayHome1": MessageLookupByLibrary.simpleMessage("Holiday Home 1"),
    "HolidayHomes": MessageLookupByLibrary.simpleMessage("Holiday Homes"),
    "HolidayHomesAgents": MessageLookupByLibrary.simpleMessage(
      "Holiday Homes Agents",
    ),
    "HomeSize": MessageLookupByLibrary.simpleMessage("Home Size"),
    "HotelName": MessageLookupByLibrary.simpleMessage("Hotel Name"),
    "Hotelname": MessageLookupByLibrary.simpleMessage("Hotel name"),
    "Hotels": MessageLookupByLibrary.simpleMessage("Hotels"),
    "Instagram": MessageLookupByLibrary.simpleMessage("Instagram"),
    "Jointhediscussion": MessageLookupByLibrary.simpleMessage(
      "Join thediscussion",
    ),
    "Kia": MessageLookupByLibrary.simpleMessage("Kia"),
    "Language": MessageLookupByLibrary.simpleMessage("Language"),
    "LargeSelectionofLuxuryHotelsforyouandyourlovedoneBookonlinetoday":
        MessageLookupByLibrary.simpleMessage(
          "Large Selection of Luxury Hotels for you and your loved one،Book online today ..",
        ),
    "Location": MessageLookupByLibrary.simpleMessage(" Location "),
    "LocationInformation": MessageLookupByLibrary.simpleMessage(
      "Location Information ",
    ),
    "Locationation": MessageLookupByLibrary.simpleMessage(
      "Location information",
    ),
    "Login": MessageLookupByLibrary.simpleMessage("Login"),
    "Logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "MainAgentInformation": MessageLookupByLibrary.simpleMessage(
      "Main Agent Information",
    ),
    "ManageFeatures": MessageLookupByLibrary.simpleMessage(
      "Manage Features,types,brands and other stuff",
    ),
    "Manageallfeaturesfromoneplace": MessageLookupByLibrary.simpleMessage(
      "Manage all features from one place",
    ),
    "MediaUpload": MessageLookupByLibrary.simpleMessage("Media Upload"),
    "Message": MessageLookupByLibrary.simpleMessage("Message"),
    "NightPrice": MessageLookupByLibrary.simpleMessage("Night Price"),
    "NoProjectsFound": MessageLookupByLibrary.simpleMessage(
      "No projects found",
    ),
    "NormalDays": MessageLookupByLibrary.simpleMessage("Normal Days"),
    "NormalPrices": MessageLookupByLibrary.simpleMessage("Normal Prices"),
    "Note": MessageLookupByLibrary.simpleMessage("Note"),
    "Notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "NumberOfDays": MessageLookupByLibrary.simpleMessage("Number Of Days"),
    "NumberofPeople": MessageLookupByLibrary.simpleMessage("Number of People"),
    "Numberofbedrooms": MessageLookupByLibrary.simpleMessage(
      "Number of bedrooms ",
    ),
    "Numberofdays": MessageLookupByLibrary.simpleMessage("Number of days"),
    "Numberofpeople": MessageLookupByLibrary.simpleMessage("Number of people"),
    "Othersettigs": MessageLookupByLibrary.simpleMessage("Other settigs"),
    "Othersettings": MessageLookupByLibrary.simpleMessage("Other settings"),
    "Password": MessageLookupByLibrary.simpleMessage("Password"),
    "Pending": MessageLookupByLibrary.simpleMessage("Pending"),
    "PhoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
    "PickupDropOff": MessageLookupByLibrary.simpleMessage("Pickup/DropOff"),
    "PleaseIwantaverycleanandbigcar": MessageLookupByLibrary.simpleMessage(
      "Please I want a very clean and big car.",
    ),
    "Pleaseiwantaverycleanandbigcar": MessageLookupByLibrary.simpleMessage(
      "Please i want a very clean and big car",
    ),
    "Price": MessageLookupByLibrary.simpleMessage("Price"),
    "PriceBreakdown": MessageLookupByLibrary.simpleMessage("Price Breakdown"),
    "PriceDetails": MessageLookupByLibrary.simpleMessage("Price Details"),
    "PriceFrom": MessageLookupByLibrary.simpleMessage("Price From"),
    "PriceRangePerDay": MessageLookupByLibrary.simpleMessage(
      "Price Range Per Day",
    ),
    "PriceRangePerNight": MessageLookupByLibrary.simpleMessage(
      "Price Range Per Night",
    ),
    "Pricepernight": MessageLookupByLibrary.simpleMessage("Price per night"),
    "Pricerangeperday": MessageLookupByLibrary.simpleMessage(
      "Price range per day",
    ),
    "Pricerangepernight": MessageLookupByLibrary.simpleMessage(
      "Price range per night",
    ),
    "Pricestartsfrom": MessageLookupByLibrary.simpleMessage(
      "Price starts from",
    ),
    "PrivateDriver": MessageLookupByLibrary.simpleMessage("Private Driver"),
    "Projects": MessageLookupByLibrary.simpleMessage("Projects"),
    "PromoSubtotal": MessageLookupByLibrary.simpleMessage("Promo Subtotal"),
    "Properety": MessageLookupByLibrary.simpleMessage("Properety"),
    "Properties": MessageLookupByLibrary.simpleMessage("Properties"),
    "Property": MessageLookupByLibrary.simpleMessage(" Property"),
    "Propertyname": MessageLookupByLibrary.simpleMessage("Property name"),
    "RangeRover": MessageLookupByLibrary.simpleMessage("Range Rover"),
    "ReUploadreelvideo": MessageLookupByLibrary.simpleMessage(
      "Re-Upload reel video",
    ),
    "RemoveApplicationfeature": MessageLookupByLibrary.simpleMessage(
      "Remove Application feature",
    ),
    "Removefromcategoryfeature": MessageLookupByLibrary.simpleMessage(
      "Remove from category feature",
    ),
    "RequestDetails": MessageLookupByLibrary.simpleMessage("Request Details"),
    "Requesteddate": MessageLookupByLibrary.simpleMessage("Requeste date"),
    "Requestedon": MessageLookupByLibrary.simpleMessage("Requestedon"),
    "Requestforcarrentapproved": MessageLookupByLibrary.simpleMessage(
      "Request for car rent approved",
    ),
    "Requests": MessageLookupByLibrary.simpleMessage("Requests"),
    "Reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "Resetpassword": MessageLookupByLibrary.simpleMessage("Reset password"),
    "Restaurants": MessageLookupByLibrary.simpleMessage("Restaurants"),
    "Resturantname": MessageLookupByLibrary.simpleMessage("Resturant name"),
    "Resturants": MessageLookupByLibrary.simpleMessage("Resturants"),
    "Reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
    "RoofTop": MessageLookupByLibrary.simpleMessage("Roof Top"),
    "Save": MessageLookupByLibrary.simpleMessage("Save"),
    "SaveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
    "ScheduleDaysPrices": MessageLookupByLibrary.simpleMessage(
      "Schedule Days Prices",
    ),
    "Search": MessageLookupByLibrary.simpleMessage("Search"),
    "SearchAreas": MessageLookupByLibrary.simpleMessage("Search Areas"),
    "SearchCars": MessageLookupByLibrary.simpleMessage("SearchCars"),
    "SearchProjects": MessageLookupByLibrary.simpleMessage("Search projects"),
    "Searchcars": MessageLookupByLibrary.simpleMessage("Search cars"),
    "Searchplacesandlocations": MessageLookupByLibrary.simpleMessage(
      "Search destinations and locations",
    ),
    "Seaview": MessageLookupByLibrary.simpleMessage("Sea view"),
    "SenddigitsOTP": MessageLookupByLibrary.simpleMessage("Send 4-digits OTP"),
    "SetLocationonmap": MessageLookupByLibrary.simpleMessage(
      "Set Location on map?",
    ),
    "Setallfeatures": MessageLookupByLibrary.simpleMessage("Set all features"),
    "Setlocationonmap": MessageLookupByLibrary.simpleMessage(
      "Set location on map?",
    ),
    "Shopname": MessageLookupByLibrary.simpleMessage("Shop name"),
    "Shops": MessageLookupByLibrary.simpleMessage("Coffee Shops"),
    "ShowSchedulePrices": MessageLookupByLibrary.simpleMessage(
      "Show Schedule Prices",
    ),
    "Status": MessageLookupByLibrary.simpleMessage("Status"),
    "Submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "Subtotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
    "Tabheretoreelvideo": MessageLookupByLibrary.simpleMessage(
      "Tab here to reel video",
    ),
    "Tabheretouploadimage": MessageLookupByLibrary.simpleMessage(
      "Tab here to upload image\'s",
    ),
    "Tabheretouploadmainvideo": MessageLookupByLibrary.simpleMessage(
      "Tab here to upload main video",
    ),
    "TabheretouploadmainvideoAr": MessageLookupByLibrary.simpleMessage(
      "Tab here to upload main arabic video ",
    ),
    "Tabheretouploadreel": MessageLookupByLibrary.simpleMessage(
      "Tab here to upload reel/s",
    ),
    "Tabheretouploadvideo": MessageLookupByLibrary.simpleMessage(
      "Tab here to upload video/s",
    ),
    "Tesla": MessageLookupByLibrary.simpleMessage("Tesla"),
    "TeslaModel3": MessageLookupByLibrary.simpleMessage("Tesla Model3"),
    "ThereAreNoUsers": MessageLookupByLibrary.simpleMessage(
      "There are no users",
    ),
    "Therearenoitems": MessageLookupByLibrary.simpleMessage(
      "There are no items",
    ),
    "Thisplaceiseverythingyouneedforthebeststay":
        MessageLookupByLibrary.simpleMessage(
          "This place is everything you need for the best stay, Perfect location, view and atmosphere.",
        ),
    "Title": MessageLookupByLibrary.simpleMessage("Title"),
    "TitleAr": MessageLookupByLibrary.simpleMessage("Arabic Title"),
    "TitleEn": MessageLookupByLibrary.simpleMessage("English Title"),
    "To": MessageLookupByLibrary.simpleMessage("To"),
    "TotalPrice": MessageLookupByLibrary.simpleMessage("TotalPrice"),
    "Tourismfee": MessageLookupByLibrary.simpleMessage("Tourism fee (5Nights)"),
    "Toyota": MessageLookupByLibrary.simpleMessage("Toyota"),
    "TranslateComment": MessageLookupByLibrary.simpleMessage(
      "Translate Comment",
    ),
    "TurnedOn": MessageLookupByLibrary.simpleMessage("Turned On"),
    "Type": MessageLookupByLibrary.simpleMessage("Type"),
    "Types": MessageLookupByLibrary.simpleMessage("Types"),
    "UploadImage": MessageLookupByLibrary.simpleMessage("Upload Image/s"),
    "UploadMainVideo": MessageLookupByLibrary.simpleMessage(
      "Upload Main Video",
    ),
    "UploadMainVideoAr": MessageLookupByLibrary.simpleMessage(
      "Upload Main Arabic Video",
    ),
    "UploadPhotos": MessageLookupByLibrary.simpleMessage("Upload Photos"),
    "UploadReel": MessageLookupByLibrary.simpleMessage("Upload Reel/s"),
    "Uploadimages": MessageLookupByLibrary.simpleMessage("Upload images"),
    "Uploadreelvideos": MessageLookupByLibrary.simpleMessage(
      "Upload reel videos",
    ),
    "Uploadvideos": MessageLookupByLibrary.simpleMessage("Upload videos"),
    "UserInformation": MessageLookupByLibrary.simpleMessage("User Information"),
    "Users": MessageLookupByLibrary.simpleMessage("Users"),
    "Vat": MessageLookupByLibrary.simpleMessage("Vat"),
    "ViewDiscussion": MessageLookupByLibrary.simpleMessage("View Discussion"),
    "WelcometoDashboard": MessageLookupByLibrary.simpleMessage(
      "Welcome to Dashboard",
    ),
    "Writedownyourdescription": MessageLookupByLibrary.simpleMessage(
      "Write down your description",
    ),
    "Year": MessageLookupByLibrary.simpleMessage("Year"),
    "Yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "YesDeletehotel": MessageLookupByLibrary.simpleMessage("Yes,Delete hotel"),
    "YouTube": MessageLookupByLibrary.simpleMessage("YouTube"),
    "YourrequestforHyundaiSonataNo": MessageLookupByLibrary.simpleMessage(
      "Your request for Hyundai Sonata No. 380282 is approved",
    ),
    "activityara": MessageLookupByLibrary.simpleMessage("Arabic Activity Name"),
    "activityeng": MessageLookupByLibrary.simpleMessage(
      "English Activity Name",
    ),
    "addFloorPlan": MessageLookupByLibrary.simpleMessage("Add Floor Plan"),
    "addMore": MessageLookupByLibrary.simpleMessage("Add More"),
    "addNewDeveloper": MessageLookupByLibrary.simpleMessage(
      "Add New Developer",
    ),
    "addNewProject": MessageLookupByLibrary.simpleMessage("Add New Project"),
    "addPricePlan": MessageLookupByLibrary.simpleMessage("Add Price Plan"),
    "addProject": MessageLookupByLibrary.simpleMessage("Add Project"),
    "addUnit": MessageLookupByLibrary.simpleMessage("Add Unit"),
    "addcar": MessageLookupByLibrary.simpleMessage("Add New Car Rental"),
    "added": MessageLookupByLibrary.simpleMessage("Added success"),
    "addedon": MessageLookupByLibrary.simpleMessage("added on"),
    "addres": MessageLookupByLibrary.simpleMessage("Add New Restaurant"),
    "allDevelopers": MessageLookupByLibrary.simpleMessage("All Developers"),
    "allcar": MessageLookupByLibrary.simpleMessage("All Car Rentals"),
    "allres": MessageLookupByLibrary.simpleMessage("All Restaurants"),
    "androidUsers": MessageLookupByLibrary.simpleMessage("Android Users"),
    "arabicDescription": MessageLookupByLibrary.simpleMessage(
      "Arabic Description",
    ),
    "arabicDescriptionSaved": MessageLookupByLibrary.simpleMessage(
      "Arabic description saved successfully",
    ),
    "arabicName": MessageLookupByLibrary.simpleMessage("Arabic Name"),
    "arabicnum": MessageLookupByLibrary.simpleMessage("Arabic Car Rental Name"),
    "arahome": MessageLookupByLibrary.simpleMessage("Arabic Holiday Home Name"),
    "arapro": MessageLookupByLibrary.simpleMessage("Arabic Property Name"),
    "areYouSureDeleteDeveloper": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this developer?",
    ),
    "areYouSureDeletePricePlan": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this price plan?",
    ),
    "areaar": MessageLookupByLibrary.simpleMessage("Area Name Arabic"),
    "areaen": MessageLookupByLibrary.simpleMessage("Area Name English"),
    "areyousureyouwanttodelete": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this",
    ),
    "areyousureyouwanttodeletethisimage": MessageLookupByLibrary.simpleMessage(
      "are you sure you want to delete this image",
    ),
    "availableUnits": MessageLookupByLibrary.simpleMessage("Available Units"),
    "bedroomsArabic": MessageLookupByLibrary.simpleMessage("Bedrooms (Arabic)"),
    "bedroomsEnglish": MessageLookupByLibrary.simpleMessage(
      "Bedrooms (English)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cannotDeleteBecauseItIsUsedInVideo": MessageLookupByLibrary.simpleMessage(
      "cannot Delete Because It Is Used In Property",
    ),
    "cannotDeleteBecauseUsed": MessageLookupByLibrary.simpleMessage(
      "Cannot delete because it is being used",
    ),
    "carr": MessageLookupByLibrary.simpleMessage("Car Rentals"),
    "cash": MessageLookupByLibrary.simpleMessage("Cash"),
    "chaletara": MessageLookupByLibrary.simpleMessage("Arabic Chalet Name"),
    "chaleteng": MessageLookupByLibrary.simpleMessage("English Chalet Name"),
    "checkemail": MessageLookupByLibrary.simpleMessage(
      "4-digit OTP Was sent to your email,Please check and enter it to continue",
    ),
    "choosecategory": MessageLookupByLibrary.simpleMessage("choose category"),
    "configurationhasbeenupdatedsuccessfully":
        MessageLookupByLibrary.simpleMessage(
          "configuration has been updated successfully",
        ),
    "confirmDelete": MessageLookupByLibrary.simpleMessage("Confirm Delete"),
    "contact": MessageLookupByLibrary.simpleMessage("Contact Us"),
    "date": MessageLookupByLibrary.simpleMessage("Date"),
    "days": MessageLookupByLibrary.simpleMessage("days"),
    "deaen": MessageLookupByLibrary.simpleMessage("English Description"),
    "deleare": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this Area, if yes you won’t be able to see it again.",
    ),
    "delecar": MessageLookupByLibrary.simpleMessage("Delete Car Rental"),
    "delemsgv": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this Video,if yes you wont be able to see again",
    ),
    "deleres": MessageLookupByLibrary.simpleMessage("Delete Restaurant"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deletePricePlan": MessageLookupByLibrary.simpleMessage(
      "Delete Price Plan",
    ),
    "deleteag": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this agent",
    ),
    "deletehome": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this Holiday Home, if yes you won’t be able to see it again.",
    ),
    "deleteimage": MessageLookupByLibrary.simpleMessage("delete image"),
    "deletepro": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this property,if yes you wont be able to see again",
    ),
    "deletepropertystatus": MessageLookupByLibrary.simpleMessage(
      "Delete Property Status",
    ),
    "deletepropertystatusDesc": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this property status, if yes you won’t be able to see it again.",
    ),
    "deletev": MessageLookupByLibrary.simpleMessage("Delete Video"),
    "deletmsgres": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this Restaurant, if yes you won’t be able to see it again.",
    ),
    "desara": MessageLookupByLibrary.simpleMessage("Arabic Description"),
    "descriptionAr": MessageLookupByLibrary.simpleMessage("Arabic Description"),
    "descriptionArabic": MessageLookupByLibrary.simpleMessage(
      "Description (Arabic)",
    ),
    "descriptionEn": MessageLookupByLibrary.simpleMessage(
      "English Description",
    ),
    "descriptionEnglish": MessageLookupByLibrary.simpleMessage(
      "Description (English)",
    ),
    "descriptions": MessageLookupByLibrary.simpleMessage("Descriptions"),
    "developer": MessageLookupByLibrary.simpleMessage("Developer"),
    "developerDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Developer deleted successfully",
    ),
    "developerDescription": MessageLookupByLibrary.simpleMessage(
      "Developer Description",
    ),
    "developerDescriptionAr": MessageLookupByLibrary.simpleMessage(
      "Developer Description (Arabic)",
    ),
    "developerDescriptionEn": MessageLookupByLibrary.simpleMessage(
      "Developer Description (English)",
    ),
    "developerDetails": MessageLookupByLibrary.simpleMessage(
      "Developer Details",
    ),
    "developerLogo": MessageLookupByLibrary.simpleMessage("Developer Logo"),
    "developerName": MessageLookupByLibrary.simpleMessage("Developer Name"),
    "developerNameAr": MessageLookupByLibrary.simpleMessage(
      "Developer Name (Arabic)",
    ),
    "developerNameEn": MessageLookupByLibrary.simpleMessage(
      "Developer Name (English)",
    ),
    "developers": MessageLookupByLibrary.simpleMessage("Developers"),
    "displayProjectInCategory": MessageLookupByLibrary.simpleMessage(
      "Display this project prominently in its category",
    ),
    "displayProjectOnHomePage": MessageLookupByLibrary.simpleMessage(
      "Display this project on the home page",
    ),
    "editPrice": MessageLookupByLibrary.simpleMessage("Edit Price"),
    "editPricePlan": MessageLookupByLibrary.simpleMessage("Edit Price Plan"),
    "editedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Edited Successfully",
    ),
    "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
    "endat": MessageLookupByLibrary.simpleMessage("End at"),
    "endsize": MessageLookupByLibrary.simpleMessage("End size"),
    "englishDescription": MessageLookupByLibrary.simpleMessage(
      "English Description",
    ),
    "englishDescriptionSaved": MessageLookupByLibrary.simpleMessage(
      "English description saved successfully",
    ),
    "englishName": MessageLookupByLibrary.simpleMessage("English Name"),
    "engnum": MessageLookupByLibrary.simpleMessage("English Car Rental Name"),
    "enhome": MessageLookupByLibrary.simpleMessage("English Holiday Home Name"),
    "enpro": MessageLookupByLibrary.simpleMessage("English Property Name"),
    "enterArabicDescription": MessageLookupByLibrary.simpleMessage(
      "Enter Arabic Description",
    ),
    "enterArabicName": MessageLookupByLibrary.simpleMessage(
      "Enter Arabic Name",
    ),
    "enterBedroomsArabic": MessageLookupByLibrary.simpleMessage(
      "Enter bedrooms in Arabic",
    ),
    "enterBedroomsEnglish": MessageLookupByLibrary.simpleMessage(
      "Enter bedrooms in English",
    ),
    "enterDate": MessageLookupByLibrary.simpleMessage("Enter date"),
    "enterDescriptionArabic": MessageLookupByLibrary.simpleMessage(
      "Enter description in Arabic",
    ),
    "enterDescriptionEnglish": MessageLookupByLibrary.simpleMessage(
      "Enter description in English",
    ),
    "enterEndingPrice": MessageLookupByLibrary.simpleMessage(
      "Enter ending price",
    ),
    "enterEnglishDescription": MessageLookupByLibrary.simpleMessage(
      "Enter English Description",
    ),
    "enterEnglishName": MessageLookupByLibrary.simpleMessage(
      "Enter English Name",
    ),
    "enterFloorPlanNameArabic": MessageLookupByLibrary.simpleMessage(
      "Enter floor plan name in Arabic",
    ),
    "enterFloorPlanNameEnglish": MessageLookupByLibrary.simpleMessage(
      "Enter floor plan name in English",
    ),
    "enterInstallmentInfo": MessageLookupByLibrary.simpleMessage(
      "Enter installment info",
    ),
    "enterOrder": MessageLookupByLibrary.simpleMessage("Enter order"),
    "enterPaymentPercentage": MessageLookupByLibrary.simpleMessage(
      "Enter payment percentage",
    ),
    "enterSpaceSizeArabic": MessageLookupByLibrary.simpleMessage(
      "Enter space size in Arabic",
    ),
    "enterSpaceSizeEnglish": MessageLookupByLibrary.simpleMessage(
      "Enter space size in English",
    ),
    "enterStartingPrice": MessageLookupByLibrary.simpleMessage(
      "Enter starting price",
    ),
    "enteraran": MessageLookupByLibrary.simpleMessage(
      "Please enter arabic name",
    ),
    "enterc": MessageLookupByLibrary.simpleMessage("Please enter category"),
    "entere": MessageLookupByLibrary.simpleMessage(
      "Please enter the email field",
    ),
    "enteremail": MessageLookupByLibrary.simpleMessage(
      "Please enter your registered email in order to send you a 4-digit OTP to reset your password",
    ),
    "enterenn": MessageLookupByLibrary.simpleMessage(
      "Please enter English name",
    ),
    "enterp": MessageLookupByLibrary.simpleMessage(
      "Please enter the password field",
    ),
    "enterpc": MessageLookupByLibrary.simpleMessage(
      "Please enter the  confirm new password field",
    ),
    "face": MessageLookupByLibrary.simpleMessage("Facebook page"),
    "failedToAddProject": MessageLookupByLibrary.simpleMessage(
      "Failed to add project",
    ),
    "failedToDeleteDeveloper": MessageLookupByLibrary.simpleMessage(
      "Failed to delete developer",
    ),
    "feaar": MessageLookupByLibrary.simpleMessage("Feature Name Arabic"),
    "feaen": MessageLookupByLibrary.simpleMessage("Feature Name English"),
    "featureName": MessageLookupByLibrary.simpleMessage("feature Name"),
    "featuredInCategory": MessageLookupByLibrary.simpleMessage(
      "Featured in Category",
    ),
    "featuredOnHome": MessageLookupByLibrary.simpleMessage("Featured on Home"),
    "featuredSettings": MessageLookupByLibrary.simpleMessage(
      "Featured Settings",
    ),
    "fill": MessageLookupByLibrary.simpleMessage(
      "Fill required fields above please",
    ),
    "fillRMSFields": MessageLookupByLibrary.simpleMessage(
      "Fill RMS Fields above please",
    ),
    "filteras": MessageLookupByLibrary.simpleMessage("Filtered As"),
    "floorPlan": MessageLookupByLibrary.simpleMessage("Floor Plan"),
    "floorPlanImage": MessageLookupByLibrary.simpleMessage("Floor Plan Image"),
    "floorPlanNameArabic": MessageLookupByLibrary.simpleMessage(
      "Name (Arabic)",
    ),
    "floorPlanNameEnglish": MessageLookupByLibrary.simpleMessage(
      "Name (English)",
    ),
    "floorPlans": MessageLookupByLibrary.simpleMessage("Floor Plans"),
    "galleryImages": MessageLookupByLibrary.simpleMessage("Gallery Images"),
    "holidaysi": MessageLookupByLibrary.simpleMessage("Property size"),
    "hotelar": MessageLookupByLibrary.simpleMessage("Arabic Hotel Name"),
    "hotenen": MessageLookupByLibrary.simpleMessage("English Hotel Name"),
    "imageSelected": MessageLookupByLibrary.simpleMessage("image selected"),
    "imagesSelected": MessageLookupByLibrary.simpleMessage("images selected"),
    "insta": MessageLookupByLibrary.simpleMessage("Instagram Page"),
    "installment": MessageLookupByLibrary.simpleMessage("Installment"),
    "iosUsers": MessageLookupByLibrary.simpleMessage("IOS Users"),
    "isPublished": MessageLookupByLibrary.simpleMessage("is Published"),
    "locationSelected": MessageLookupByLibrary.simpleMessage(
      "Location selected",
    ),
    "manage": MessageLookupByLibrary.simpleMessage(
      "Manage features, types, brands and other stuff",
    ),
    "moving": MessageLookupByLibrary.simpleMessage("Moving"),
    "noResultFound": MessageLookupByLibrary.simpleMessage("No Result Found"),
    "nofea": MessageLookupByLibrary.simpleMessage("There are no Features"),
    "numroom": MessageLookupByLibrary.simpleMessage("Number of bedrooms"),
    "order": MessageLookupByLibrary.simpleMessage("Order"),
    "passcur": MessageLookupByLibrary.simpleMessage("Current Password"),
    "passwordchangedsuccessfullly": MessageLookupByLibrary.simpleMessage(
      "password changed successfullly",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("Payment (%)"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "paymentPlan": MessageLookupByLibrary.simpleMessage("Payment Plan"),
    "paymentPlans": MessageLookupByLibrary.simpleMessage("Payment Plans"),
    "pleaseEnterArabicDescription": MessageLookupByLibrary.simpleMessage(
      "Please enter Arabic description",
    ),
    "pleaseEnterArabicName": MessageLookupByLibrary.simpleMessage(
      "Please enter Arabic name",
    ),
    "pleaseEnterEnglishDescription": MessageLookupByLibrary.simpleMessage(
      "Please enter English description",
    ),
    "pleaseEnterEnglishName": MessageLookupByLibrary.simpleMessage(
      "Please enter English name",
    ),
    "pleaseSelectLocation": MessageLookupByLibrary.simpleMessage(
      "Please select location",
    ),
    "pleaseSelectPricePlan": MessageLookupByLibrary.simpleMessage(
      "Please select a price plan",
    ),
    "pleaseSelectPropertyStatus": MessageLookupByLibrary.simpleMessage(
      "Please select property status",
    ),
    "pleaseSelectType": MessageLookupByLibrary.simpleMessage(
      "Please select a type",
    ),
    "pleaseusenewpasswordtologin": MessageLookupByLibrary.simpleMessage(
      "please use new password to login",
    ),
    "priceFrom": MessageLookupByLibrary.simpleMessage("Price From"),
    "pricePlan": MessageLookupByLibrary.simpleMessage("Price Plan"),
    "priceTo": MessageLookupByLibrary.simpleMessage("Price To"),
    "pricepernight": MessageLookupByLibrary.simpleMessage("Price Per Night"),
    "priprice": MessageLookupByLibrary.simpleMessage("Private drive price"),
    "projectAddedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Project added successfully",
    ),
    "promo": MessageLookupByLibrary.simpleMessage("Promo Codes"),
    "propertyStatus": MessageLookupByLibrary.simpleMessage("Property Status"),
    "reraNumber": MessageLookupByLibrary.simpleMessage("RERA Number"),
    "reraPermit": MessageLookupByLibrary.simpleMessage("RERA Permit"),
    "reraPermitImage": MessageLookupByLibrary.simpleMessage(
      "RERA Permit Image",
    ),
    "reraPermitImageSelected": MessageLookupByLibrary.simpleMessage(
      "RERA permit image selected",
    ),
    "resara": MessageLookupByLibrary.simpleMessage("Arabic Restaurant Name"),
    "resen": MessageLookupByLibrary.simpleMessage("English Restaurant Name"),
    "reset": MessageLookupByLibrary.simpleMessage("reset"),
    "rmsProperties": MessageLookupByLibrary.simpleMessage("RMS Properties"),
    "rmsUnits": MessageLookupByLibrary.simpleMessage("Units"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "searchDevelopers": MessageLookupByLibrary.simpleMessage(
      "Search Developers",
    ),
    "searchRmsArea": MessageLookupByLibrary.simpleMessage("Search Area"),
    "searchRmsDiscount": MessageLookupByLibrary.simpleMessage(
      "Search RMS Discount",
    ),
    "searchRmsProperty": MessageLookupByLibrary.simpleMessage(
      "Search RMS Property",
    ),
    "searchRmsUnit": MessageLookupByLibrary.simpleMessage("Search RMS Unit"),
    "selectDeveloper": MessageLookupByLibrary.simpleMessage("Select Developer"),
    "selectLocation": MessageLookupByLibrary.simpleMessage("Select Location"),
    "selectPaymentMethod": MessageLookupByLibrary.simpleMessage(
      "Select Payment Method",
    ),
    "selectRmsArea": MessageLookupByLibrary.simpleMessage("Select RMS Area"),
    "selectRmsDiscount": MessageLookupByLibrary.simpleMessage(
      "Select RMS Discount",
    ),
    "selectRmsProperty": MessageLookupByLibrary.simpleMessage(
      "Select RMS Property",
    ),
    "selectRmsUnit": MessageLookupByLibrary.simpleMessage("Select RMS Unit"),
    "selectimg": MessageLookupByLibrary.simpleMessage("Select images please"),
    "selectvid": MessageLookupByLibrary.simpleMessage(
      "Select main video please",
    ),
    "setallfeatures": MessageLookupByLibrary.simpleMessage("setallfeatures"),
    "shopar": MessageLookupByLibrary.simpleMessage("Arabic Shop Name"),
    "shopen": MessageLookupByLibrary.simpleMessage("English Shop Name"),
    "spaceSizeArabic": MessageLookupByLibrary.simpleMessage(
      "Space Size (Arabic)",
    ),
    "spaceSizeEnglish": MessageLookupByLibrary.simpleMessage(
      "Space Size (English)",
    ),
    "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
    "starting": MessageLookupByLibrary.simpleMessage("Starting"),
    "startsize": MessageLookupByLibrary.simpleMessage("Start size"),
    "tabHereToUploadReraPermitImage": MessageLookupByLibrary.simpleMessage(
      "Tap here to upload RERA permit image",
    ),
    "tapp": MessageLookupByLibrary.simpleMessage("Tab here to upload photo"),
    "termar": MessageLookupByLibrary.simpleMessage("Arabic Terms And Condtion"),
    "termen": MessageLookupByLibrary.simpleMessage(
      "English Terms And Condtion",
    ),
    "type": MessageLookupByLibrary.simpleMessage("Type"),
    "typear": MessageLookupByLibrary.simpleMessage("Type Name Arabic"),
    "typeen": MessageLookupByLibrary.simpleMessage("Type Name English"),
    "unit": MessageLookupByLibrary.simpleMessage("Unit"),
    "updatepro": MessageLookupByLibrary.simpleMessage(
      "Profile was updated successfuly",
    ),
    "usersusedPromoCode": MessageLookupByLibrary.simpleMessage(
      "20 users used Promo Code",
    ),
    "usersusedpromocode": MessageLookupByLibrary.simpleMessage(
      "users used promo code",
    ),
    "villa": MessageLookupByLibrary.simpleMessage("villa"),
    "website": MessageLookupByLibrary.simpleMessage("Website"),
    "websiteAr": MessageLookupByLibrary.simpleMessage("Arabic Website"),
    "whatsapp": MessageLookupByLibrary.simpleMessage("Whatsapp"),
    "wrong": MessageLookupByLibrary.simpleMessage(
      "Something went wrong, please try again later",
    ),
    "yDeleteAgent": MessageLookupByLibrary.simpleMessage("Yes,Delete Agent"),
    "yeDeleteproperty": MessageLookupByLibrary.simpleMessage(
      "yes,Delete property",
    ),
    "yesDeleteHolidayhome": MessageLookupByLibrary.simpleMessage(
      "yes,Delete Holiday home",
    ),
    "yesDeleteShop": MessageLookupByLibrary.simpleMessage("yes,DeleteShop"),
    "yesDeleteactivity": MessageLookupByLibrary.simpleMessage(
      "yes,Delete activity",
    ),
    "yesDeletebrand": MessageLookupByLibrary.simpleMessage("yes,Delete brand"),
    "yesDeletechalet": MessageLookupByLibrary.simpleMessage(
      "yes,Delete chalet",
    ),
    "yesDeletefeature": MessageLookupByLibrary.simpleMessage(
      "yes,Delete feature",
    ),
    "yesDeletereelvideo": MessageLookupByLibrary.simpleMessage(
      "yes,Deletereel video",
    ),
    "yesDeleterentalcar": MessageLookupByLibrary.simpleMessage(
      "yes,Delete rental car",
    ),
    "yesDeleteresturant": MessageLookupByLibrary.simpleMessage(
      "yes,Delete resturant",
    ),
    "yesDeletetype": MessageLookupByLibrary.simpleMessage("yes,Delete type"),
    "yesDeleteyear": MessageLookupByLibrary.simpleMessage("yes,Delete year"),
    "yesde": MessageLookupByLibrary.simpleMessage("Yes, Delete"),
    "yesdecar": MessageLookupByLibrary.simpleMessage("Yes, Delete Car Rent"),
  };
}
