import 'package:admin_dubai/src/features/views/projects/projects_page.dart';
import 'package:flutter/material.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../api_provider/property_api_provider.dart';

void deleteProject(id, index,
    {required BuildContext context, required isLoading}) {
  showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Container(
                height: MediaQuery.of(context).size.height * 0.3,
                decoration: new BoxDecoration(
                    color: const Color(0xffF5F6F7),
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25.0),
                        topRight: Radius.circular(25.0)),
                    border: Border.all(color: Colors.black, width: 1.0)),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                        height: 5, width: 50, color: const Color(0xffD2D4D6)),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).DeleteProject,
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).AreYouSureYouWantToDeleteThisProject,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            height: 40,
                            width: 100,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.grey),
                            child: Center(
                              child: Text(
                                S.of(context).Cancel,
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 16),
                              ),
                            ),
                          ),
                        ),
                        StatefulBuilder(builder: (context, refrechState) {
                          return GestureDetector(
                            onTap: () async {
                              if (!isLoading) {
                                refrechState(() {
                                  isLoading = true;
                                });

                                var response = await PropertyApiProvider()
                                    .deletePropertie(id);
                                print(response.code);
                                print(response.msg ?? '');
                                if (response.code == 1) {
                                  Navigator.pop(context);

                                  Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                          builder: (_) => ProjectsPage()));

                                  snackbar(response.msg ?? '');
                                } else {
                                  Navigator.pop(context);
                                  snackbar(response.msg ?? '');
                                }
                                refrechState(() {
                                  isLoading = false;
                                });
                              }
                            },
                            child: Container(
                              height: 40,
                              width: 100,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.red),
                              child: Center(
                                child: isLoading
                                    ? const ADLinearProgressIndicator()
                                    : Text(
                                        S.of(context).delete,
                                        style: const TextStyle(
                                            color: Colors.white, fontSize: 16),
                                      ),
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ],
                )),
          ));
}
