import 'dart:io';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/multi_main_category_drop_down.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/other_settings_api_provider.dart';
import '../../../../response/generalResponse.dart';

class AddType extends StatefulWidget {
  const AddType({super.key});

  @override
  _AddType createState() => _AddType();
}

class _AddType extends State<AddType> {
  // List<String> category = [
  //   'holidayHome',
  //   'carRent',
  //   'luxury',
  //   'shops',
  //   'activites'
  // ];
  List<MainCategoryModel> selectedCategories = [];
  TextEditingController namearController = TextEditingController();
  TextEditingController nameenController = TextEditingController();
  OtherSettingsApiProvider provider = OtherSettingsApiProvider();
  bool isLoading = false;
  File? _image;

  submit(FormData data) async {
    if (_image == null) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text(S.of(context).selectimg),
      ));
    }

    if (namearController.text == "") {
      snackbar(S.of(context).enteraran);

      return;
    }
    if (nameenController.text == "") {
      snackbar(S.of(context).enterenn);

      return;
    }
    setState(() {
      isLoading = true;
    });

    // pr.show();
    final GeneralResponse successInformation = await provider.addtype(data);
    // pr.hide();
    if (successInformation.code == 1) {
      Navigator.pop(context);
//
//
    } else {
      if (successInformation.msg == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation.msg!);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).Addnewtype),
      ),
      body: Container(
        padding: const EdgeInsets.all(20),
        child: ListView(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MultiCategoryDropDown(
              selectedCategories: selectedCategories,
              onChanged: (value) {
                setState(() {
                  selectedCategories = value;
                });
              },
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              S.of(context).typeen,
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 0.5, color: Colors.grey),
                  color: Colors.white),
              child: Container(
                  padding: const EdgeInsets.only(
                      left: 20, right: 20, top: 15, bottom: 15),
                  child: TextFormField(
                    controller: nameenController,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: S.of(context).typeen,
                        hintStyle: const TextStyle(
                          color: Color(0xffB7B7B7),
                          fontSize: 14,
                        )),
                  )),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              S.of(context).typear,
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 0.5, color: Colors.grey),
                  color: Colors.white),
              child: Container(
                  padding: const EdgeInsets.only(
                      left: 20, right: 20, top: 15, bottom: 15),
                  child: TextFormField(
                    controller: namearController,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: S.of(context).typear,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  )),
            ),
            const SizedBox(
              height: 10,
            ),
            ADFilePicker(
              onSingleFileSelected: (images) =>
                  _image = [images].isNotEmpty ? [images][0] : null,
              title: S.of(context).Tabheretouploadimage,
              type: FileType.media,
              isMultiple: false,
            ),
            const SizedBox(
              height: 20,
            ),
            !isLoading
                ? SizedBox(
                    height: 60,
                    child: GestureDetector(
                        onTap: () async {
                          FormData form = FormData.fromMap({
                            'name[en]': nameenController.text,
                            "name[ar]": namearController.text,
                            'category': selectedCategories
                                .map((e) => e.id)
                                .toString()
                                .replaceAll("(", "")
                                .replaceAll(")", "")
                            // "category": 8,
                            // currentvalue2?.id,
                          });

                          if (_image != null) {
                            form.files.add(MapEntry(
                              "image",
                              await MultipartFile.fromFile(_image!.path),
                            ));
                          }

                          submit(form);
                        },
                        child: Container(
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: GlobalColors.primaryColor,
                              borderRadius: BorderRadius.circular(5)),
                          child: Container(
                              padding: const EdgeInsets.all(10),
                              child: Center(
                                  child: Text(
                                S.of(context).Addnewtype,
                                style: const TextStyle(color: Colors.white),
                              ))),
                        )))
                : const ADLinearProgressIndicator()
          ],
        ),
      ),
    ));
  }
}
